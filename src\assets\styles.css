@import url("https://fonts.googleapis.com/css2?family=Fira+Code:wght@500&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Orbitron:wght@400..900&display=swap");
@import "tailwindcss";
@import "tw-animate-css";
@import "../styles/responsive.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0); /* white */
  --foreground: oklch(0.145 0 0); /* black */
  --card: oklch(1 0 0); /* white */
  --card-foreground: oklch(0.145 0 0); /* black */
  --popover: oklch(1 0 0); /* white */
  --popover-foreground: oklch(0.145 0 0); /* black */

  /* Primary: emerald-700 #047857 - Use strategically for buttons, links, and important UI elements */
  --primary: oklch(0.51 0.158 160);
  --primary-foreground: oklch(1 0 0); /* white */

  /* Secondary: slate-700 #334155 - Neutral dark color for secondary elements */
  --secondary: oklch(0.4 0.02 240);
  --secondary-foreground: oklch(0.95 0 0); /* light gray */

  /* Muted: slate-100 #f1f5f9 - Light neutral background */
  --muted: oklch(0.97 0.01 240);
  --muted-foreground: oklch(0.5 0.02 240); /* slate-500 */

  /* Accent: slate-200 #e2e8f0 - Subtle accent for hover states */
  --accent: oklch(0.93 0.01 240);
  --accent-foreground: oklch(0.3 0.02 240); /* slate-800 */

  --destructive: oklch(0.577 0.245 27.325); /* red */

  /* Border/Focus ring: emerald-300 #6ee7b7 - Keep emerald for focus states */
  --border: oklch(0.9 0.01 240); /* slate-200 */
  --input: oklch(0.93 0.01 240); /* slate-200 */
  --ring: oklch(0.85 0.12 160); /* emerald-300 */

  /* Chart colors - keep diverse */
  --chart-1: oklch(0.51 0.158 160); /* emerald-700 */
  --chart-2: oklch(0.6 0.118 184.704); /* blue */
  --chart-3: oklch(0.398 0.07 227.392); /* purple */
  --chart-4: oklch(0.828 0.189 84.429); /* orange */
  --chart-5: oklch(0.769 0.188 70.08); /* yellow */

  /* Sidebar - use emerald as an accent, but keep it mostly neutral */
  --sidebar: oklch(0.985 0 0); /* white */
  --sidebar-foreground: oklch(0.145 0 0); /* black */
  --sidebar-primary: oklch(0.51 0.158 160); /* emerald-700 */
  --sidebar-primary-foreground: oklch(1 0 0); /* white */
  --sidebar-accent: oklch(0.93 0.01 240); /* slate-200 */
  --sidebar-accent-foreground: oklch(0.3 0.02 240); /* slate-800 */
  --sidebar-border: oklch(0.9 0.01 240); /* slate-200 */
  --sidebar-ring: oklch(0.85 0.12 160); /* emerald-300 */
}

.dark {
  --background: oklch(0.145 0 0); /* dark gray/black */
  --foreground: oklch(0.985 0 0); /* white */
  --card: oklch(0.18 0 0); /* slightly lighter than background */
  --card-foreground: oklch(0.985 0 0); /* white */
  --popover: oklch(0.18 0 0); /* slightly lighter than background */
  --popover-foreground: oklch(0.985 0 0); /* white */

  /* Primary: emerald-500 #10b981 in dark mode - brighter for dark backgrounds */
  --primary: oklch(0.72 0.17 160);
  --primary-foreground: oklch(0.145 0 0); /* black */

  /* Secondary: slate-600 #475569 - Neutral color for secondary elements */
  --secondary: oklch(0.45 0.02 240);
  --secondary-foreground: oklch(0.95 0 0); /* light gray */

  /* Muted: slate-800 #1e293b - Dark neutral background */
  --muted: oklch(0.25 0.02 240);
  --muted-foreground: oklch(0.7 0.02 240); /* slate-400 */

  /* Accent: slate-700 #334155 - Subtle accent for hover states */
  --accent: oklch(0.3 0.02 240);
  --accent-foreground: oklch(0.9 0.01 240); /* slate-200 */

  --destructive: oklch(0.704 0.191 22.216); /* red */

  /* Border/Focus ring: emerald-400 #34d399 with reduced opacity - Keep emerald for focus states */
  --border: oklch(0.3 0.02 240 / 30%); /* slate-700 with opacity */
  --input: oklch(0.25 0.02 240 / 30%); /* slate-800 with opacity */
  --ring: oklch(0.72 0.17 160 / 30%); /* emerald-500 with opacity */

  /* Chart colors - keep diverse */
  --chart-1: oklch(0.72 0.17 160); /* emerald-500 */
  --chart-2: oklch(0.696 0.17 162.48); /* blue */
  --chart-3: oklch(0.769 0.188 70.08); /* yellow */
  --chart-4: oklch(0.627 0.265 303.9); /* purple */
  --chart-5: oklch(0.645 0.246 16.439); /* red */

  /* Sidebar - use emerald as an accent, but keep it mostly neutral */
  --sidebar: oklch(0.18 0 0); /* slightly lighter than background */
  --sidebar-foreground: oklch(0.985 0 0); /* white */
  --sidebar-primary: oklch(0.72 0.17 160); /* emerald-500 */
  --sidebar-primary-foreground: oklch(0.145 0 0); /* black */
  --sidebar-accent: oklch(0.3 0.02 240); /* slate-700 */
  --sidebar-accent-foreground: oklch(0.9 0.01 240); /* slate-200 */
  --sidebar-border: oklch(0.3 0.02 240 / 30%); /* slate-700 with opacity */
  --sidebar-ring: oklch(0.72 0.17 160 / 30%); /* emerald-500 with opacity */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: "Fira Code", monospace;
    font-optical-sizing: auto;
    font-weight: 500;
    font-style: normal;
  }
}

/* Custom utility classes */
@layer utilities {
  /* Font classes */

  .font-secondary {
    font-family: "Orbitron", sans-serif;
    font-weight: 600;
    color: theme("colors.emerald.700");
  }

  /* Logo styling classes */

  .logo-large {
    height: 4.5rem;
    width: auto;
  }

  .logo-medium {
    height: 3.5rem;
    width: auto;
  }

  .logo-container {
    display: flex;
    align-items: center;
  }

  /* Custom scrollbar styles for Kanban board */
  .kanban-scroll {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) transparent;
  }

  .kanban-scroll::-webkit-scrollbar {
    height: 8px;
    width: 8px;
  }

  .kanban-scroll::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  .kanban-scroll::-webkit-scrollbar-thumb {
    background: rgb(203 213 225);
    border-radius: 4px;
    border: 1px solid transparent;
  }

  .kanban-scroll::-webkit-scrollbar-thumb:hover {
    background: rgb(148 163 184);
  }

  .kanban-scroll::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Kanban column auto-height support */
  .kanban-column {
    min-height: fit-content;
    height: auto;
  }

  /* Smooth scrolling for the entire page when columns get tall */
  .kanban-container {
    scroll-behavior: smooth;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}
