name: CI

on:
  push:
    branches: [ main-backup ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      
      - name: Install pnpm
        run: npm install -g pnpm

      - uses: actions/setup-node@v4
        with:
          node-version: '22.x'
          cache: 'pnpm'

     
      - run: pnpm -v
      - run: which pnpm

      - run: pnpm install
      - run: pnpm lint
      - run: pnpm build
